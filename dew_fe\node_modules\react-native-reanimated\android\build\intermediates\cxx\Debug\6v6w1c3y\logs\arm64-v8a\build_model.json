{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6v6w1c3y\\arm64-v8a", "soFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6v6w1c3y\\obj\\arm64-v8a", "soRepublishFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_TOOLCHAIN=clang", "-DREACT_NATIVE_DIR=D:/side_project/dew_fe/node_modules/react-native", "-DJS_RUNTIME=hermes", "-DJS_RUNTIME_DIR=D:\\side_project\\dew_fe\\node_modules\\react-native\\sdks\\hermes", "-DIS_NEW_ARCHITECTURE_ENABLED=true", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.17.5", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DHERMES_ENABLE_DEBUGGER=1"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a", "x86_64"], "buildTargetSet": ["worklets", "reanimated"], "implicitBuildTargetSet": ["reanimated", "worklets"], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\.cxx", "intermediatesBaseFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates", "intermediatesFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-reanimated", "moduleRootFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android", "moduleBuildFile": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build.gradle", "makeFile": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\side_project\\dew_fe\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fa03d1ffcf2d88d54ed6c7d80f87d2da\\transformed\\hermes-android-0.79.5-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6v6w1c3y\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6v6w1c3y3z582o4q4s635w6b4i5s631f5o3y4b4lp604h272r1l484b3f1h3op", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.8.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/side_project/dew_fe/node_modules/react-native-reanimated/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-<PERSON><PERSON><PERSON>_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/side_project/dew_fe/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/side_project/dew_fe/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=D:/side_project/dew_fe/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BD:/side_project/dew_fe/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DREACT_NATIVE_MINOR_VERSION=79\n-DANDROID_TOOLCHAIN=clang\n-DREACT_NATIVE_DIR=D:/side_project/dew_fe/node_modules/react-native\n-DJS_RUNTIME=hermes\n-DJS_RUNTIME_DIR=D:/side_project/dew_fe/node_modules/react-native/sdks/hermes\n-DIS_NEW_ARCHITECTURE_ENABLED=true\n-DIS_REANIMATED_EXAMPLE_APP=false\n-DREANIMATED_VERSION=3.17.5\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n-DHERMES_ENABLE_DEBUGGER=1", "configurationArguments": ["-HD:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6v6w1c3y\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6v6w1c3y\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6v6w1c3y\\prefab\\arm64-v8a\\prefab", "-BD:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6v6w1c3y\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_TOOLCHAIN=clang", "-DREACT_NATIVE_DIR=D:/side_project/dew_fe/node_modules/react-native", "-DJS_RUNTIME=hermes", "-DJS_RUNTIME_DIR=D:\\side_project\\dew_fe\\node_modules\\react-native\\sdks\\hermes", "-DIS_NEW_ARCHITECTURE_ENABLED=true", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.17.5", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DHERMES_ENABLE_DEBUGGER=1"], "stlLibraryFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "D:\\side_project\\dew_fe\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6v6w1c3y"}